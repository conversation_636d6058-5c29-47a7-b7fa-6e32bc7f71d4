@import "tailwindcss";

/* Smooth scrolling for jump links */
html {
  scroll-behavior: smooth;
}

/* CSS Custom Properties for viewport height */
:root {
  --vh: 1vh;
}

/* Set viewport height custom property */
@media screen and (max-width: 768px) {
  :root {
    --vh: 1vh;
  }
}

/* Mobile menu animations and sizing */
.mobile-menu {
  transform: translateX(-100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background-color: white !important;
  /* Ensure full height on all mobile devices */
  min-height: 100vh;
  min-height: 100dvh;
  max-height: 100vh;
  max-height: 100dvh;
}

.mobile-menu.open {
  transform: translateX(0);
}

/* Fix for iOS Safari viewport issues */
@supports (-webkit-touch-callout: none) {
  .mobile-menu {
    height: -webkit-fill-available;
  }
}

/* Mobile menu touch targets */
.mobile-nav-link {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  touch-action: manipulation;
}

/* Improved mobile menu backdrop */
#mobile-backdrop {
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ensure navigation stays above backdrop */
nav {
  position: relative;
  z-index: 60;
}

/* Hamburger menu animation */
.hamburger-line {
  transition: all 0.3s ease-in-out;
  transform-origin: center;
}

.hamburger.open .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger.open .hamburger-line:nth-child(2) {
  opacity: 0;
}

.hamburger.open .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Focus styles for accessibility */
.nav-link:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Backdrop blur support */
@supports (backdrop-filter: blur(10px)) {
  .nav-backdrop {
    backdrop-filter: blur(10px);
  }
}