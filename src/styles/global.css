@import "tailwindcss";

/* Smooth scrolling for jump links */
html {
  scroll-behavior: smooth;
}

/* Mobile menu animations */
.mobile-menu {
  transform: translateX(-100%);
  transition: transform 0.3s ease-in-out;
}

.mobile-menu.open {
  transform: translateX(0);
}

/* Hamburger menu animation */
.hamburger-line {
  transition: all 0.3s ease-in-out;
  transform-origin: center;
}

.hamburger.open .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger.open .hamburger-line:nth-child(2) {
  opacity: 0;
}

.hamburger.open .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Focus styles for accessibility */
.nav-link:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Backdrop blur support */
@supports (backdrop-filter: blur(10px)) {
  .nav-backdrop {
    backdrop-filter: blur(10px);
  }
}