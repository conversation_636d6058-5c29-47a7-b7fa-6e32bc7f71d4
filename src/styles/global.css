@import "tailwindcss";

/* Shabby Chic Fonts */
@font-face {
  font-family: 'Allura';
  src: url('/fonts/Allura-Regular.woff2') format('woff2'),
       url('/fonts/Allura-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Playfair Display';
  src: url('/fonts/PlayfairDisplay-Regular.woff2') format('woff2'),
       url('/fonts/PlayfairDisplay-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Playfair Display';
  src: url('/fonts/PlayfairDisplay-Bold.woff2') format('woff2'),
       url('/fonts/PlayfairDisplay-Bold.woff') format('woff');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Roboto';
  src: url('/fonts/Roboto-Regular.woff2') format('woff2'),
       url('/fonts/Roboto-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Roboto';
  src: url('/fonts/Roboto-Medium.woff2') format('woff2'),
       url('/fonts/Roboto-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}

/* Shabby Chic CSS Variables */
:root {
  --color-shabby-pink: #F8E7E8;
  --color-shabby-blue: #EFF9FA;
  --color-dusty-rose: #E8B4B8;
  --color-sage-green: #B8C5B8;
  --color-lavender: #E8E0F0;
  --color-cream: #FAF7F4;
  --color-charcoal: #333333;
  --color-soft-white: #FEFEFE;

  /* Font stacks with fallbacks for immediate use */
  --font-allura: 'Allura', 'Dancing Script', 'Brush Script MT', cursive;
  --font-playfair: 'Playfair Display', 'Times New Roman', 'Georgia', serif;
  --font-roboto: 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
}

/* Base Typography */
body {
  font-family: var(--font-roboto);
  color: var(--color-charcoal);
  background-color: var(--color-soft-white);
}

/* Smooth scrolling for jump links */
html {
  scroll-behavior: smooth;
}

/* CSS Custom Properties for viewport height */
:root {
  --vh: 1vh;
}

/* Set viewport height custom property */
@media screen and (max-width: 768px) {
  :root {
    --vh: 1vh;
  }
}

/* Mobile menu animations and sizing */
.mobile-menu {
  transform: translateX(-100%);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background-color: white !important;
  /* Ensure full height on all mobile devices */
  min-height: 100vh;
  min-height: 100dvh;
  max-height: 100vh;
  max-height: 100dvh;
}

.mobile-menu.open {
  transform: translateX(0);
}

/* Fix for iOS Safari viewport issues */
@supports (-webkit-touch-callout: none) {
  .mobile-menu {
    height: -webkit-fill-available;
  }
}

/* Mobile menu touch targets */
.mobile-nav-link {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  touch-action: manipulation;
}

/* Improved mobile menu backdrop */
#mobile-backdrop {
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smart sticky navigation */
#main-nav {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 60 !important;
  transform: translateY(0);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#main-nav.-translate-y-full {
  transform: translateY(-100%);
}

/* Ensure smooth transitions on mobile */
@media (max-width: 767px) {
  #main-nav {
    will-change: transform;
  }
}

/* Hamburger menu animation */
.hamburger-line {
  transition: all 0.3s ease-in-out;
  transform-origin: center;
}

.hamburger.open .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.hamburger.open .hamburger-line:nth-child(2) {
  opacity: 0;
}

.hamburger.open .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Focus styles for accessibility */
.nav-link:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Shabby Chic Typography Classes */
.font-allura {
  font-family: var(--font-allura);
}

.font-playfair {
  font-family: var(--font-playfair);
}

.font-roboto {
  font-family: var(--font-roboto);
}

/* Shabby Chic Backgrounds */
.bg-shabby-pink {
  background-color: var(--color-shabby-pink);
}

.bg-shabby-blue {
  background-color: var(--color-shabby-blue);
}

.bg-dusty-rose {
  background-color: var(--color-dusty-rose);
}

.bg-sage-green {
  background-color: var(--color-sage-green);
}

.bg-lavender {
  background-color: var(--color-lavender);
}

.bg-cream {
  background-color: var(--color-cream);
}

/* Shabby Chic Text Colors */
.text-dusty-rose {
  color: var(--color-dusty-rose);
}

.text-sage-green {
  color: var(--color-sage-green);
}

.text-charcoal {
  color: var(--color-charcoal);
}

/* Shabby Chic Decorative Elements */
.vintage-border {
  border: 2px solid var(--color-dusty-rose);
  border-radius: 8px;
  position: relative;
}

.vintage-border::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 1px solid var(--color-shabby-pink);
  border-radius: 12px;
  z-index: -1;
}

.lace-pattern {
  background-image:
    radial-gradient(circle at 25% 25%, var(--color-shabby-pink) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, var(--color-lavender) 1px, transparent 1px);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
}

.distressed-shadow {
  box-shadow:
    0 4px 6px -1px rgba(232, 180, 184, 0.3),
    0 2px 4px -1px rgba(232, 180, 184, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.vintage-button {
  background: linear-gradient(135deg, var(--color-dusty-rose) 0%, #d4a5a9 100%);
  border: 2px solid var(--color-dusty-rose);
  border-radius: 8px;
  color: white;
  font-family: var(--font-playfair);
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.vintage-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.vintage-button:hover::before {
  left: 100%;
}

.vintage-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 12px rgba(232, 180, 184, 0.4);
}

.vintage-button:active {
  transform: translateY(0);
}

/* Floral Accent */
.floral-accent::after {
  content: '🌸';
  position: absolute;
  top: -8px;
  right: -8px;
  font-size: 16px;
  opacity: 0.7;
}

/* Shabby Chic Form Styling */
.shabby-input {
  border: 2px solid var(--color-shabby-pink);
  border-radius: 8px;
  background-color: var(--color-cream);
  font-family: var(--font-roboto);
  transition: all 0.3s ease;
}

.shabby-input:focus {
  outline: none;
  border-color: var(--color-dusty-rose);
  background-color: white;
  box-shadow: 0 0 0 3px rgba(232, 180, 184, 0.1);
}

.shabby-card {
  background: linear-gradient(135deg, var(--color-cream) 0%, white 100%);
  border: 1px solid var(--color-shabby-pink);
  border-radius: 16px;
  box-shadow:
    0 10px 25px rgba(232, 180, 184, 0.15),
    0 4px 10px rgba(232, 180, 184, 0.1);
}

/* Backdrop blur support with shabby chic colors */
.nav-backdrop {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background-color: rgba(250, 247, 244, 0.9) !important;
}

/* Fallback for browsers without backdrop-filter support */
@supports not (backdrop-filter: blur(10px)) {
  .nav-backdrop {
    background-color: var(--color-cream) !important;
  }
}