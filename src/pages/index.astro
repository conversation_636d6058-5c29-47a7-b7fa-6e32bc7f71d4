---
import Layout from '../layouts/Layout.astro';
---

<Layout title="Bouquet Garden - Beautiful Flower Gardens">
	<!-- Home Section -->
	<section id="home" class="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center">
		<div class="max-w-4xl mx-auto px-4 text-center">
			<h1 class="text-5xl md:text-7xl font-bold text-gray-900 mb-6">
				Welcome to <span class="text-green-600">Bouquet Garden</span>
			</h1>
			<p class="text-xl md:text-2xl text-gray-600 mb-8">
				Discover the beauty of nature in our stunning flower gardens
			</p>
			<a href="#about" class="inline-block bg-green-600 text-white px-8 py-3 rounded-lg text-lg font-semibold hover:bg-green-700 transition-colors duration-200">
				Explore Our Gardens
			</a>
		</div>
	</section>

	<!-- About Section -->
	<section id="about" class="py-20 bg-white">
		<div class="max-w-6xl mx-auto px-4">
			<div class="text-center mb-16">
				<h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">About Our Garden</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					Our bouquet garden is a carefully curated collection of the most beautiful flowers,
					designed to inspire and delight visitors of all ages.
				</p>
			</div>
			<div class="grid md:grid-cols-2 gap-12 items-center">
				<div>
					<h3 class="text-2xl font-bold text-gray-900 mb-4">Our Story</h3>
					<p class="text-gray-600 mb-6">
						Founded with a passion for horticulture and natural beauty, our garden has been
						cultivating exceptional flower varieties for over a decade. We believe in the power
						of flowers to bring joy, peace, and inspiration to everyone who visits.
					</p>
					<p class="text-gray-600">
						From rare exotic blooms to classic garden favorites, every flower in our collection
						is grown with care and attention to detail.
					</p>
				</div>
				<div class="bg-gray-200 h-64 rounded-lg flex items-center justify-center">
					<span class="text-gray-500">Garden Image Placeholder</span>
				</div>
			</div>
		</div>
	</section>

	<!-- Flowers Section -->
	<section id="flowers" class="py-20 bg-gray-50">
		<div class="max-w-6xl mx-auto px-4">
			<div class="text-center mb-16">
				<h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Our Flowers</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					Explore our diverse collection of beautiful flowers, each carefully selected and nurtured.
				</p>
			</div>
			<div class="grid md:grid-cols-3 gap-8">
				<div class="bg-white rounded-lg shadow-md overflow-hidden">
					<div class="bg-gray-200 h-48 flex items-center justify-center">
						<span class="text-gray-500">Rose Garden</span>
					</div>
					<div class="p-6">
						<h3 class="text-xl font-bold text-gray-900 mb-2">Roses</h3>
						<p class="text-gray-600">Classic and hybrid roses in various colors and fragrances.</p>
					</div>
				</div>
				<div class="bg-white rounded-lg shadow-md overflow-hidden">
					<div class="bg-gray-200 h-48 flex items-center justify-center">
						<span class="text-gray-500">Tulip Field</span>
					</div>
					<div class="p-6">
						<h3 class="text-xl font-bold text-gray-900 mb-2">Tulips</h3>
						<p class="text-gray-600">Vibrant tulips that bloom in spectacular spring displays.</p>
					</div>
				</div>
				<div class="bg-white rounded-lg shadow-md overflow-hidden">
					<div class="bg-gray-200 h-48 flex items-center justify-center">
						<span class="text-gray-500">Wildflower Meadow</span>
					</div>
					<div class="p-6">
						<h3 class="text-xl font-bold text-gray-900 mb-2">Wildflowers</h3>
						<p class="text-gray-600">Natural wildflower meadows with native species.</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Book Visit Section -->
	<section id="book-visit" class="py-20 bg-green-600 text-white">
		<div class="max-w-4xl mx-auto px-4 text-center">
			<h2 class="text-4xl md:text-5xl font-bold mb-6">Book Your Visit</h2>
			<p class="text-xl mb-8 opacity-90">
				Plan your perfect garden experience with us. Choose from guided tours,
				photography sessions, or peaceful self-guided walks.
			</p>
			<div class="grid md:grid-cols-3 gap-6 mb-12">
				<div class="bg-white/10 rounded-lg p-6">
					<h3 class="text-xl font-bold mb-3">Guided Tours</h3>
					<p class="opacity-90">Expert-led tours with detailed plant information</p>
				</div>
				<div class="bg-white/10 rounded-lg p-6">
					<h3 class="text-xl font-bold mb-3">Photography</h3>
					<p class="opacity-90">Perfect settings for professional or personal photos</p>
				</div>
				<div class="bg-white/10 rounded-lg p-6">
					<h3 class="text-xl font-bold mb-3">Self-Guided</h3>
					<p class="opacity-90">Explore at your own pace with our garden map</p>
				</div>
			</div>
			<button class="bg-white text-green-600 px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors duration-200">
				Reserve Your Spot
			</button>
		</div>
	</section>

	<!-- Location Section -->
	<section id="location" class="py-20 bg-white">
		<div class="max-w-6xl mx-auto px-4">
			<div class="text-center mb-16">
				<h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Visit Us</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					Find us in the heart of the countryside, easily accessible by car or public transport.
				</p>
			</div>
			<div class="grid md:grid-cols-2 gap-12">
				<div>
					<h3 class="text-2xl font-bold text-gray-900 mb-6">Location Details</h3>
					<div class="space-y-4">
						<div class="flex items-start space-x-3">
							<div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
								<span class="text-white text-xs">📍</span>
							</div>
							<div>
								<p class="font-semibold text-gray-900">Address</p>
								<p class="text-gray-600">123 Garden Lane, Flower Valley, State 12345</p>
							</div>
						</div>
						<div class="flex items-start space-x-3">
							<div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
								<span class="text-white text-xs">🕒</span>
							</div>
							<div>
								<p class="font-semibold text-gray-900">Hours</p>
								<p class="text-gray-600">Daily: 9:00 AM - 6:00 PM</p>
								<p class="text-gray-600">Closed Mondays (except holidays)</p>
							</div>
						</div>
						<div class="flex items-start space-x-3">
							<div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
								<span class="text-white text-xs">🚗</span>
							</div>
							<div>
								<p class="font-semibold text-gray-900">Parking</p>
								<p class="text-gray-600">Free parking available on-site</p>
							</div>
						</div>
					</div>
				</div>
				<div class="bg-gray-200 h-64 rounded-lg flex items-center justify-center">
					<span class="text-gray-500">Map Placeholder</span>
				</div>
			</div>
		</div>
	</section>

	<!-- What to Expect Section -->
	<section id="what-to-expect" class="py-20 bg-gray-50">
		<div class="max-w-6xl mx-auto px-4">
			<div class="text-center mb-16">
				<h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">What to Expect</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					Make the most of your visit with these helpful tips and information.
				</p>
			</div>
			<div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
				<div class="text-center">
					<div class="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
						<span class="text-white text-2xl">🌸</span>
					</div>
					<h3 class="text-lg font-bold text-gray-900 mb-2">Seasonal Blooms</h3>
					<p class="text-gray-600">Different flowers bloom throughout the year</p>
				</div>
				<div class="text-center">
					<div class="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
						<span class="text-white text-2xl">👟</span>
					</div>
					<h3 class="text-lg font-bold text-gray-900 mb-2">Comfortable Shoes</h3>
					<p class="text-gray-600">Wear comfortable walking shoes for garden paths</p>
				</div>
				<div class="text-center">
					<div class="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
						<span class="text-white text-2xl">📷</span>
					</div>
					<h3 class="text-lg font-bold text-gray-900 mb-2">Photography</h3>
					<p class="text-gray-600">Cameras welcome for personal use</p>
				</div>
				<div class="text-center">
					<div class="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
						<span class="text-white text-2xl">☀️</span>
					</div>
					<h3 class="text-lg font-bold text-gray-900 mb-2">Weather Ready</h3>
					<p class="text-gray-600">Check weather and dress appropriately</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Contact Section -->
	<section id="contact" class="py-20 bg-white">
		<div class="max-w-4xl mx-auto px-4">
			<div class="text-center mb-16">
				<h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Get in Touch</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					Have questions or want to plan a special event? We'd love to hear from you.
				</p>
			</div>
			<div class="grid md:grid-cols-2 gap-12">
				<div>
					<h3 class="text-2xl font-bold text-gray-900 mb-6">Contact Information</h3>
					<div class="space-y-4">
						<div class="flex items-center space-x-3">
							<div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center">
								<span class="text-white text-xs">📞</span>
							</div>
							<div>
								<p class="font-semibold text-gray-900">Phone</p>
								<p class="text-gray-600">(*************</p>
							</div>
						</div>
						<div class="flex items-center space-x-3">
							<div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center">
								<span class="text-white text-xs">✉️</span>
							</div>
							<div>
								<p class="font-semibold text-gray-900">Email</p>
								<p class="text-gray-600"><EMAIL></p>
							</div>
						</div>
						<div class="flex items-center space-x-3">
							<div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center">
								<span class="text-white text-xs">💬</span>
							</div>
							<div>
								<p class="font-semibold text-gray-900">Social Media</p>
								<p class="text-gray-600">Follow us @bouquetgarden</p>
							</div>
						</div>
					</div>
				</div>
				<div>
					<h3 class="text-2xl font-bold text-gray-900 mb-6">Send us a Message</h3>
					<form class="space-y-4">
						<div>
							<label for="name" class="block text-sm font-medium text-gray-700 mb-1">Name</label>
							<input type="text" id="name" name="name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
						</div>
						<div>
							<label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
							<input type="email" id="email" name="email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
						</div>
						<div>
							<label for="message" class="block text-sm font-medium text-gray-700 mb-1">Message</label>
							<textarea id="message" name="message" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"></textarea>
						</div>
						<button type="submit" class="w-full bg-green-600 text-white px-6 py-3 rounded-md font-semibold hover:bg-green-700 transition-colors duration-200">
							Send Message
						</button>
					</form>
				</div>
			</div>
		</div>
	</section>
</Layout>
