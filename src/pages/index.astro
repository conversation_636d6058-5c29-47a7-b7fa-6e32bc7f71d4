---
import Layout from '../layouts/Layout.astro';
---

<Layout title="Bouquet Garden - Beautiful Flower Gardens">
	<!-- Hero Section -->
	<section id="home" class="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center py-20">
		<div class="max-w-7xl mx-auto px-4 w-full">
			<div class="grid lg:grid-cols-5 gap-12 items-center">

				<!-- Left Column - Content -->
				<div class="lg:col-span-3 space-y-8">
					<div class="space-y-6">
						<h1 class="text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 leading-tight">
							Visit Our Beautiful <span class="text-green-600">Bouquet Garden</span>
						</h1>
						<p class="text-xl md:text-2xl text-gray-600 leading-relaxed">
							Discover stunning flower displays perfect for photography, relaxation, and creating lasting memories in our carefully curated garden paradise.
						</p>
					</div>

					<!-- Key Information -->
					<div class="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
						<h3 class="text-lg font-semibold text-gray-900 mb-4">Visit Information</h3>
						<div class="grid sm:grid-cols-2 gap-4 text-sm">
							<div class="flex items-center space-x-3">
								<div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
									<span class="text-green-600 text-sm">📍</span>
								</div>
								<div>
									<p class="font-medium text-gray-900">Location</p>
									<p class="text-gray-600">123 Garden Lane, Flower Valley</p>
								</div>
							</div>
							<div class="flex items-center space-x-3">
								<div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
									<span class="text-green-600 text-sm">🕒</span>
								</div>
								<div>
									<p class="font-medium text-gray-900">Hours</p>
									<p class="text-gray-600">Daily 9AM-6PM</p>
									<p class="text-gray-500 text-xs">Closed Mondays</p>
								</div>
							</div>
							<div class="flex items-center space-x-3">
								<div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
									<span class="text-green-600 text-sm">📞</span>
								</div>
								<div>
									<p class="font-medium text-gray-900">Contact</p>
									<p class="text-gray-600">(*************</p>
								</div>
							</div>
							<div class="flex items-center space-x-3">
								<div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
									<span class="text-green-600 text-sm">🚗</span>
								</div>
								<div>
									<p class="font-medium text-gray-900">Parking</p>
									<p class="text-gray-600">Free on-site parking</p>
								</div>
							</div>
						</div>
					</div>

					<!-- Mobile CTA Buttons - Show only on mobile -->
					<div class="lg:hidden flex flex-col sm:flex-row gap-4">
						<button onclick="scrollToBooking()" class="flex-1 bg-green-600 text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-green-700 transition-colors duration-200 shadow-lg">
							Book Your Visit - $35
						</button>
						<a href="#what-to-expect" class="flex-1 border-2 border-green-600 text-green-600 px-8 py-4 rounded-xl text-lg font-semibold hover:bg-green-50 transition-colors duration-200 text-center">
							What to Expect
						</a>
					</div>
				</div>

				<!-- Right Column - Booking Form -->
				<div class="lg:col-span-2">
					<div class="bg-white rounded-3xl shadow-2xl p-6 lg:p-8 border border-gray-100">
						<!-- Vintage Wheelbarrow Illustration Placeholder - Smaller on desktop -->
						<div class="mb-4 lg:mb-6 text-center">
							<div class="w-full h-32 lg:h-40 bg-gradient-to-br from-amber-50 to-green-50 rounded-xl flex items-center justify-center border-2 border-dashed border-green-200">
								<div class="text-center">
									<div class="text-4xl lg:text-5xl mb-1">🌸</div>
									<p class="text-xs lg:text-sm text-gray-500 font-medium">Vintage Wheelbarrow</p>
									<p class="text-xs text-gray-400">Full of Beautiful Flowers</p>
								</div>
							</div>
						</div>

						<div class="text-center mb-4 lg:mb-6">
							<h2 class="text-xl lg:text-2xl font-bold text-gray-900 mb-1 lg:mb-2">Book Your Garden Visit</h2>
							<p class="text-sm lg:text-base text-gray-600">Experience our beautiful gardens</p>
							<div class="mt-2 lg:mt-3 inline-flex items-center bg-green-100 text-green-800 px-3 lg:px-4 py-1.5 lg:py-2 rounded-full text-sm font-semibold">
								<span class="text-base lg:text-lg mr-1">💰</span>
								$35 per person
							</div>
						</div>

						<!-- Booking Form -->
						<form id="booking-form" class="space-y-3">
							<!-- Desktop: 2-column layout, Mobile: single column -->
							<div class="grid grid-cols-1 lg:grid-cols-2 gap-3">
								<div>
									<label for="fullName" class="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
									<input
										type="text"
										id="fullName"
										name="fullName"
										required
										class="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors text-sm"
										placeholder="Your full name"
									>
								</div>
								<div>
									<label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email *</label>
									<input
										type="email"
										id="email"
										name="email"
										required
										class="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors text-sm"
										placeholder="<EMAIL>"
									>
								</div>
								<div>
									<label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number *</label>
									<input
										type="tel"
										id="phone"
										name="phone"
										required
										class="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors text-sm"
										placeholder="(*************"
									>
								</div>
								<div>
									<label for="visitDate" class="block text-sm font-medium text-gray-700 mb-1">Visit Date *</label>
									<input
										type="date"
										id="visitDate"
										name="visitDate"
										required
										class="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors text-sm"
									>
								</div>
								<div>
									<label for="preferredTime" class="block text-sm font-medium text-gray-700 mb-1">Preferred Time *</label>
									<select
										id="preferredTime"
										name="preferredTime"
										required
										class="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors text-sm"
									>
										<option value="">Select time</option>
										<option value="9:00 AM">9:00 AM</option>
										<option value="10:00 AM">10:00 AM</option>
										<option value="11:00 AM">11:00 AM</option>
										<option value="12:00 PM">12:00 PM</option>
										<option value="1:00 PM">1:00 PM</option>
										<option value="2:00 PM">2:00 PM</option>
										<option value="3:00 PM">3:00 PM</option>
										<option value="4:00 PM">4:00 PM</option>
										<option value="5:00 PM">5:00 PM</option>
									</select>
								</div>
								<div>
									<label for="numberOfVisitors" class="block text-sm font-medium text-gray-700 mb-1">Number of Visitors</label>
									<select
										id="numberOfVisitors"
										name="numberOfVisitors"
										class="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors text-sm"
									>
										<option value="1">1 person</option>
										<option value="2">2 people</option>
										<option value="3">3 people</option>
										<option value="4">4 people</option>
										<option value="5">5 people</option>
										<option value="6">6 people</option>
										<option value="7">7 people</option>
										<option value="8">8 people</option>
										<option value="9">9 people</option>
										<option value="10+">10+ people (call us)</option>
									</select>
								</div>
							</div>

							<!-- Total Price Display -->
							<div class="bg-gray-50 rounded-lg p-3 border border-gray-200">
								<div class="flex justify-between items-center">
									<span class="text-gray-700 font-medium text-sm">Total Cost:</span>
									<span id="totalPrice" class="text-xl font-bold text-green-600">$35</span>
								</div>
								<p class="text-xs text-gray-500 mt-1">Price per person: $35</p>
							</div>

							<!-- Action Buttons - Side by side on desktop, stacked on mobile -->
							<div class="grid grid-cols-1 lg:grid-cols-2 gap-2 pt-2">
								<button
									type="submit"
									id="bookNowBtn"
									class="bg-green-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-green-700 transition-colors duration-200 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed text-sm lg:text-base"
								>
									<span id="bookingBtnText">Book Now & Pay</span>
									<span id="bookingSpinner" class="hidden">
										<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
											<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
											<path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
										</svg>
										Processing...
									</span>
								</button>
								<a
									href="#what-to-expect"
									class="border-2 border-green-600 text-green-600 px-6 py-3 rounded-lg font-semibold hover:bg-green-50 transition-colors duration-200 text-center text-sm lg:text-base"
								>
									What to Expect
								</a>
							</div>

							<p class="text-xs text-gray-500 text-center mt-2">
								Secure payment powered by Stripe. Your information is protected.
							</p>
						</form>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- About Section -->
	<section id="about" class="py-20 bg-white">
		<div class="max-w-6xl mx-auto px-4">
			<div class="text-center mb-16">
				<h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">About Our Garden</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					Our bouquet garden is a carefully curated collection of the most beautiful flowers,
					designed to inspire and delight visitors of all ages.
				</p>
			</div>
			<div class="grid md:grid-cols-2 gap-12 items-center">
				<div>
					<h3 class="text-2xl font-bold text-gray-900 mb-4">Our Story</h3>
					<p class="text-gray-600 mb-6">
						Founded with a passion for horticulture and natural beauty, our garden has been
						cultivating exceptional flower varieties for over a decade. We believe in the power
						of flowers to bring joy, peace, and inspiration to everyone who visits.
					</p>
					<p class="text-gray-600">
						From rare exotic blooms to classic garden favorites, every flower in our collection
						is grown with care and attention to detail.
					</p>
				</div>
				<div class="bg-gray-200 h-64 rounded-lg flex items-center justify-center">
					<span class="text-gray-500">Garden Image Placeholder</span>
				</div>
			</div>
		</div>
	</section>

	<!-- Flowers Section -->
	<section id="flowers" class="py-20 bg-gray-50">
		<div class="max-w-6xl mx-auto px-4">
			<div class="text-center mb-16">
				<h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Our Flowers</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					Explore our diverse collection of beautiful flowers, each carefully selected and nurtured.
				</p>
			</div>
			<div class="grid md:grid-cols-3 gap-8">
				<div class="bg-white rounded-lg shadow-md overflow-hidden">
					<div class="bg-gray-200 h-48 flex items-center justify-center">
						<span class="text-gray-500">Rose Garden</span>
					</div>
					<div class="p-6">
						<h3 class="text-xl font-bold text-gray-900 mb-2">Roses</h3>
						<p class="text-gray-600">Classic and hybrid roses in various colors and fragrances.</p>
					</div>
				</div>
				<div class="bg-white rounded-lg shadow-md overflow-hidden">
					<div class="bg-gray-200 h-48 flex items-center justify-center">
						<span class="text-gray-500">Tulip Field</span>
					</div>
					<div class="p-6">
						<h3 class="text-xl font-bold text-gray-900 mb-2">Tulips</h3>
						<p class="text-gray-600">Vibrant tulips that bloom in spectacular spring displays.</p>
					</div>
				</div>
				<div class="bg-white rounded-lg shadow-md overflow-hidden">
					<div class="bg-gray-200 h-48 flex items-center justify-center">
						<span class="text-gray-500">Wildflower Meadow</span>
					</div>
					<div class="p-6">
						<h3 class="text-xl font-bold text-gray-900 mb-2">Wildflowers</h3>
						<p class="text-gray-600">Natural wildflower meadows with native species.</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	<!-- Book Visit Section -->
	<section id="book-visit" class="py-20 bg-green-600 text-white">
		<div class="max-w-4xl mx-auto px-4 text-center">
			<h2 class="text-4xl md:text-5xl font-bold mb-6">Book Your Visit</h2>
			<p class="text-xl mb-8 opacity-90">
				Plan your perfect garden experience with us. Choose from guided tours,
				photography sessions, or peaceful self-guided walks.
			</p>
			<div class="grid md:grid-cols-3 gap-6 mb-12">
				<div class="bg-white/10 rounded-lg p-6">
					<h3 class="text-xl font-bold mb-3">Guided Tours</h3>
					<p class="opacity-90">Expert-led tours with detailed plant information</p>
				</div>
				<div class="bg-white/10 rounded-lg p-6">
					<h3 class="text-xl font-bold mb-3">Photography</h3>
					<p class="opacity-90">Perfect settings for professional or personal photos</p>
				</div>
				<div class="bg-white/10 rounded-lg p-6">
					<h3 class="text-xl font-bold mb-3">Self-Guided</h3>
					<p class="opacity-90">Explore at your own pace with our garden map</p>
				</div>
			</div>
			<button class="bg-white text-green-600 px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gray-100 transition-colors duration-200">
				Reserve Your Spot
			</button>
		</div>
	</section>

	<!-- Location Section -->
	<section id="location" class="py-20 bg-white">
		<div class="max-w-6xl mx-auto px-4">
			<div class="text-center mb-16">
				<h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Visit Us</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					Find us in the heart of the countryside, easily accessible by car or public transport.
				</p>
			</div>
			<div class="grid md:grid-cols-2 gap-12">
				<div>
					<h3 class="text-2xl font-bold text-gray-900 mb-6">Location Details</h3>
					<div class="space-y-4">
						<div class="flex items-start space-x-3">
							<div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
								<span class="text-white text-xs">📍</span>
							</div>
							<div>
								<p class="font-semibold text-gray-900">Address</p>
								<p class="text-gray-600">123 Garden Lane, Flower Valley, State 12345</p>
							</div>
						</div>
						<div class="flex items-start space-x-3">
							<div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
								<span class="text-white text-xs">🕒</span>
							</div>
							<div>
								<p class="font-semibold text-gray-900">Hours</p>
								<p class="text-gray-600">Daily: 9:00 AM - 6:00 PM</p>
								<p class="text-gray-600">Closed Mondays (except holidays)</p>
							</div>
						</div>
						<div class="flex items-start space-x-3">
							<div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
								<span class="text-white text-xs">🚗</span>
							</div>
							<div>
								<p class="font-semibold text-gray-900">Parking</p>
								<p class="text-gray-600">Free parking available on-site</p>
							</div>
						</div>
					</div>
				</div>
				<div class="bg-gray-200 h-64 rounded-lg flex items-center justify-center">
					<span class="text-gray-500">Map Placeholder</span>
				</div>
			</div>
		</div>
	</section>

	<!-- What to Expect Section -->
	<section id="what-to-expect" class="py-20 bg-gray-50">
		<div class="max-w-6xl mx-auto px-4">
			<div class="text-center mb-16">
				<h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">What to Expect</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					Make the most of your visit with these helpful tips and information.
				</p>
			</div>
			<div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
				<div class="text-center">
					<div class="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
						<span class="text-white text-2xl">🌸</span>
					</div>
					<h3 class="text-lg font-bold text-gray-900 mb-2">Seasonal Blooms</h3>
					<p class="text-gray-600">Different flowers bloom throughout the year</p>
				</div>
				<div class="text-center">
					<div class="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
						<span class="text-white text-2xl">👟</span>
					</div>
					<h3 class="text-lg font-bold text-gray-900 mb-2">Comfortable Shoes</h3>
					<p class="text-gray-600">Wear comfortable walking shoes for garden paths</p>
				</div>
				<div class="text-center">
					<div class="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
						<span class="text-white text-2xl">📷</span>
					</div>
					<h3 class="text-lg font-bold text-gray-900 mb-2">Photography</h3>
					<p class="text-gray-600">Cameras welcome for personal use</p>
				</div>
				<div class="text-center">
					<div class="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
						<span class="text-white text-2xl">☀️</span>
					</div>
					<h3 class="text-lg font-bold text-gray-900 mb-2">Weather Ready</h3>
					<p class="text-gray-600">Check weather and dress appropriately</p>
				</div>
			</div>
		</div>
	</section>

	<!-- Contact Section -->
	<section id="contact" class="py-20 bg-white">
		<div class="max-w-4xl mx-auto px-4">
			<div class="text-center mb-16">
				<h2 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Get in Touch</h2>
				<p class="text-xl text-gray-600 max-w-3xl mx-auto">
					Have questions or want to plan a special event? We'd love to hear from you.
				</p>
			</div>
			<div class="grid md:grid-cols-2 gap-12">
				<div>
					<h3 class="text-2xl font-bold text-gray-900 mb-6">Contact Information</h3>
					<div class="space-y-4">
						<div class="flex items-center space-x-3">
							<div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center">
								<span class="text-white text-xs">📞</span>
							</div>
							<div>
								<p class="font-semibold text-gray-900">Phone</p>
								<p class="text-gray-600">(*************</p>
							</div>
						</div>
						<div class="flex items-center space-x-3">
							<div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center">
								<span class="text-white text-xs">✉️</span>
							</div>
							<div>
								<p class="font-semibold text-gray-900">Email</p>
								<p class="text-gray-600"><EMAIL></p>
							</div>
						</div>
						<div class="flex items-center space-x-3">
							<div class="w-6 h-6 bg-green-600 rounded-full flex items-center justify-center">
								<span class="text-white text-xs">💬</span>
							</div>
							<div>
								<p class="font-semibold text-gray-900">Social Media</p>
								<p class="text-gray-600">Follow us @bouquetgarden</p>
							</div>
						</div>
					</div>
				</div>
				<div>
					<h3 class="text-2xl font-bold text-gray-900 mb-6">Send us a Message</h3>
					<form class="space-y-4">
						<div>
							<label for="name" class="block text-sm font-medium text-gray-700 mb-1">Name</label>
							<input type="text" id="name" name="name" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
						</div>
						<div>
							<label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
							<input type="email" id="email" name="email" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
						</div>
						<div>
							<label for="message" class="block text-sm font-medium text-gray-700 mb-1">Message</label>
							<textarea id="message" name="message" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"></textarea>
						</div>
						<button type="submit" class="w-full bg-green-600 text-white px-6 py-3 rounded-md font-semibold hover:bg-green-700 transition-colors duration-200">
							Send Message
						</button>
					</form>
				</div>
			</div>
		</div>
	</section>
</Layout>

<script>
	// Booking form functionality
	document.addEventListener('DOMContentLoaded', function() {
		const numberOfVisitorsSelect = document.getElementById('numberOfVisitors') as HTMLSelectElement;
		const totalPriceElement = document.getElementById('totalPrice') as HTMLElement;
		const bookingForm = document.getElementById('booking-form') as HTMLFormElement;
		const bookNowBtn = document.getElementById('bookNowBtn') as HTMLButtonElement;
		const bookingBtnText = document.getElementById('bookingBtnText') as HTMLElement;
		const bookingSpinner = document.getElementById('bookingSpinner') as HTMLElement;
		const visitDateInput = document.getElementById('visitDate') as HTMLInputElement;

		const PRICE_PER_PERSON = 35;

		// Set minimum date to today
		const today = new Date().toISOString().split('T')[0];
		if (visitDateInput) {
			visitDateInput.min = today;
		}

		// Update total price when number of visitors changes
		function updateTotalPrice() {
			const numberOfVisitors = parseInt(numberOfVisitorsSelect?.value || '1');
			const totalPrice = numberOfVisitors * PRICE_PER_PERSON;
			if (totalPriceElement) {
				totalPriceElement.textContent = `$${totalPrice}`;
			}
		}

		// Initialize price calculation
		updateTotalPrice();
		numberOfVisitorsSelect?.addEventListener('change', updateTotalPrice);

		// Scroll to booking form (for mobile CTA)
		(window as any).scrollToBooking = function() {
			const bookingForm = document.getElementById('booking-form');
			if (bookingForm) {
				bookingForm.scrollIntoView({ behavior: 'smooth', block: 'center' });
			}
		};

		// Form validation
		function validateForm() {
			const requiredFields = ['fullName', 'email', 'phone', 'visitDate', 'preferredTime'];
			let isValid = true;

			requiredFields.forEach(fieldId => {
				const field = document.getElementById(fieldId) as HTMLInputElement | HTMLSelectElement;
				if (field && !field.value.trim()) {
					field.classList.add('border-red-500');
					isValid = false;
				} else if (field) {
					field.classList.remove('border-red-500');
				}
			});

			// Email validation
			const emailField = document.getElementById('email') as HTMLInputElement;
			if (emailField && emailField.value) {
				const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
				if (!emailRegex.test(emailField.value)) {
					emailField.classList.add('border-red-500');
					isValid = false;
				}
			}

			// Phone validation (basic)
			const phoneField = document.getElementById('phone') as HTMLInputElement;
			if (phoneField && phoneField.value) {
				const cleanPhone = phoneField.value.replace(/\D/g, '');
				if (cleanPhone.length < 10) {
					phoneField.classList.add('border-red-500');
					isValid = false;
				}
			}

			return isValid;
		}

		// Handle form submission
		bookingForm?.addEventListener('submit', async function(e) {
			e.preventDefault();

			if (!validateForm()) {
				alert('Please fill in all required fields correctly.');
				return;
			}

			// Show loading state
			bookNowBtn.disabled = true;
			bookingBtnText.classList.add('hidden');
			bookingSpinner.classList.remove('hidden');

			try {
				// Collect form data
				const formData = new FormData(bookingForm);
				const numberOfVisitors = parseInt((formData.get('numberOfVisitors') as string) || '1');
				const totalAmount = numberOfVisitors * PRICE_PER_PERSON;

				const bookingData = {
					fullName: formData.get('fullName') as string,
					email: formData.get('email') as string,
					phone: formData.get('phone') as string,
					visitDate: formData.get('visitDate') as string,
					preferredTime: formData.get('preferredTime') as string,
					numberOfVisitors: numberOfVisitors,
					totalAmount: totalAmount
				};

				// TODO: Replace with actual Stripe integration
				// For now, simulate booking process
				await simulateBookingProcess(bookingData);

			} catch (error) {
				console.error('Booking error:', error);
				alert('There was an error processing your booking. Please try again or call us at (*************.');
			} finally {
				// Reset button state
				bookNowBtn.disabled = false;
				bookingBtnText.classList.remove('hidden');
				bookingSpinner.classList.add('hidden');
			}
		});

		// Simulate booking process (replace with actual Stripe integration)
		async function simulateBookingProcess(bookingData: any) {
			// Simulate API call delay
			await new Promise(resolve => setTimeout(resolve, 2000));

			// For demo purposes, show success message
			alert(`Booking simulation successful!\n\nDetails:\n- Name: ${bookingData.fullName}\n- Date: ${bookingData.visitDate}\n- Time: ${bookingData.preferredTime}\n- Visitors: ${bookingData.numberOfVisitors}\n- Total: $${bookingData.totalAmount}\n\nIn production, this would integrate with Stripe for payment processing.`);

			// Reset form
			if (bookingForm) {
				bookingForm.reset();
				updateTotalPrice();
			}
		}

		// Phone number formatting
		const phoneInput = document.getElementById('phone') as HTMLInputElement;
		phoneInput?.addEventListener('input', function(e) {
			const target = e.target as HTMLInputElement;
			if (target) {
				let value = target.value.replace(/\D/g, '');
				if (value.length >= 6) {
					value = value.replace(/(\d{3})(\d{3})(\d{4})/, '($1) $2-$3');
				} else if (value.length >= 3) {
					value = value.replace(/(\d{3})(\d{0,3})/, '($1) $2');
				}
				target.value = value;
			}
		});
	});
</script>
