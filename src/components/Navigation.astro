---
// Navigation component with responsive design and accessibility features
---

<nav class="fixed top-0 left-0 right-0 z-50 bg-white/90 nav-backdrop border-b border-gray-200">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-16">
      
      <!-- Logo Section -->
      <div class="flex-shrink-0">
        <a href="#home" class="flex items-center space-x-2 nav-link">
          <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
            <span class="text-white font-bold text-sm">BG</span>
          </div>
          <span class="font-semibold text-gray-900 text-lg">Bouquet Garden</span>
        </a>
      </div>

      <!-- Desktop Navigation -->
      <div class="hidden md:block">
        <div class="ml-10 flex items-baseline space-x-8">
          <a href="#home" class="nav-link text-gray-900 hover:text-green-600 px-3 py-2 text-sm font-medium transition-colors duration-200">
            Home
          </a>
          <a href="#about" class="nav-link text-gray-900 hover:text-green-600 px-3 py-2 text-sm font-medium transition-colors duration-200">
            About
          </a>
          <a href="#flowers" class="nav-link text-gray-900 hover:text-green-600 px-3 py-2 text-sm font-medium transition-colors duration-200">
            Flowers
          </a>
          <a href="#book-visit" class="nav-link text-gray-900 hover:text-green-600 px-3 py-2 text-sm font-medium transition-colors duration-200">
            Book Visit
          </a>
          <a href="#location" class="nav-link text-gray-900 hover:text-green-600 px-3 py-2 text-sm font-medium transition-colors duration-200">
            Location
          </a>
          <a href="#what-to-expect" class="nav-link text-gray-900 hover:text-green-600 px-3 py-2 text-sm font-medium transition-colors duration-200">
            What to Expect
          </a>
          <a href="#contact" class="nav-link text-gray-900 hover:text-green-600 px-3 py-2 text-sm font-medium transition-colors duration-200">
            Contact
          </a>
        </div>
      </div>

      <!-- Mobile menu button -->
      <div class="md:hidden">
        <button
          type="button"
          class="hamburger inline-flex items-center justify-center p-2 rounded-md text-gray-900 hover:text-green-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-green-500"
          aria-controls="mobile-menu"
          aria-expanded="false"
          id="mobile-menu-button"
        >
          <span class="sr-only">Open main menu</span>
          <div class="w-6 h-6 flex flex-col justify-center items-center">
            <span class="hamburger-line block w-5 h-0.5 bg-current mb-1"></span>
            <span class="hamburger-line block w-5 h-0.5 bg-current mb-1"></span>
            <span class="hamburger-line block w-5 h-0.5 bg-current"></span>
          </div>
        </button>
      </div>
    </div>
  </div>

  <!-- Mobile menu backdrop -->
  <div class="md:hidden fixed inset-0 bg-black bg-opacity-50 hidden z-40" id="mobile-backdrop"></div>

  <!-- Mobile menu -->
  <div class="md:hidden mobile-menu fixed inset-y-0 left-0 w-64 bg-white shadow-xl z-50" id="mobile-menu">
    <div class="flex flex-col h-full bg-white">
      <!-- Mobile menu header -->
      <div class="flex items-center justify-between p-4 border-b border-gray-200 bg-white">
        <div class="flex items-center space-x-2">
          <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center">
            <span class="text-white font-bold text-sm">BG</span>
          </div>
          <span class="font-semibold text-gray-900">Bouquet Garden</span>
        </div>
        <button
          type="button"
          class="p-2 rounded-md text-gray-900 hover:text-green-600 hover:bg-gray-100"
          id="mobile-menu-close"
        >
          <span class="sr-only">Close menu</span>
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Mobile menu items -->
      <div class="flex-1 px-4 py-6 space-y-1 bg-white">
        <a href="#home" class="mobile-nav-link block px-3 py-2 text-base font-medium text-gray-900 hover:text-green-600 hover:bg-gray-50 rounded-md transition-colors duration-200">
          Home
        </a>
        <a href="#about" class="mobile-nav-link block px-3 py-2 text-base font-medium text-gray-900 hover:text-green-600 hover:bg-gray-50 rounded-md transition-colors duration-200">
          About
        </a>
        <a href="#flowers" class="mobile-nav-link block px-3 py-2 text-base font-medium text-gray-900 hover:text-green-600 hover:bg-gray-50 rounded-md transition-colors duration-200">
          Flowers
        </a>
        <a href="#book-visit" class="mobile-nav-link block px-3 py-2 text-base font-medium text-gray-900 hover:text-green-600 hover:bg-gray-50 rounded-md transition-colors duration-200">
          Book Visit
        </a>
        <a href="#location" class="mobile-nav-link block px-3 py-2 text-base font-medium text-gray-900 hover:text-green-600 hover:bg-gray-50 rounded-md transition-colors duration-200">
          Location
        </a>
        <a href="#what-to-expect" class="mobile-nav-link block px-3 py-2 text-base font-medium text-gray-900 hover:text-green-600 hover:bg-gray-50 rounded-md transition-colors duration-200">
          What to Expect
        </a>
        <a href="#contact" class="mobile-nav-link block px-3 py-2 text-base font-medium text-gray-900 hover:text-green-600 hover:bg-gray-50 rounded-md transition-colors duration-200">
          Contact
        </a>
      </div>
    </div>
  </div>
</nav>

<script>
  // Mobile menu functionality
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');
  const mobileMenuClose = document.getElementById('mobile-menu-close');
  const mobileBackdrop = document.getElementById('mobile-backdrop');
  const hamburger = document.querySelector('.hamburger');
  const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');

  function openMobileMenu() {
    mobileMenu?.classList.add('open');
    mobileBackdrop?.classList.remove('hidden');
    hamburger?.classList.add('open');
    mobileMenuButton?.setAttribute('aria-expanded', 'true');
    document.body.style.overflow = 'hidden';
  }

  function closeMobileMenu() {
    mobileMenu?.classList.remove('open');
    mobileBackdrop?.classList.add('hidden');
    hamburger?.classList.remove('open');
    mobileMenuButton?.setAttribute('aria-expanded', 'false');
    document.body.style.overflow = '';
  }

  // Event listeners
  mobileMenuButton?.addEventListener('click', openMobileMenu);
  mobileMenuClose?.addEventListener('click', closeMobileMenu);
  mobileBackdrop?.addEventListener('click', closeMobileMenu);

  // Close mobile menu when clicking on nav links
  mobileNavLinks.forEach(link => {
    link.addEventListener('click', closeMobileMenu);
  });

  // Close mobile menu on escape key
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && mobileMenu?.classList.contains('open')) {
      closeMobileMenu();
    }
  });

  // Handle window resize
  window.addEventListener('resize', () => {
    if (window.innerWidth >= 768) {
      closeMobileMenu();
    }
  });
</script>
